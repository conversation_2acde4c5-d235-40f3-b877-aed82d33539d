# Build directories and CMake artifacts
build/
_deps/
_build/
cmake-build-*/

# Build outputs
*.a
*.so
*.dylib
*.dll
*.exe

# CMake generated files
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile
compile_commands.json
CTestTestfile.cmake
Testing/
install_manifest*.txt

# IDE files
.idea/
*.code-workspace
.vs/

# OS files
.DS_Store
Thumbs.db
*.swp
*.swo
*~

# Temporary files
*.tmp
*.temp
*.log
build_output.log

# Coverage and profiling
*.gcda
*.gcno
*.profraw
coverage/

# Package files
*.deb
*.rpm
*.dmg
*.msi
*.pkg

# Local configuration
.env
.env.local

# Git lock files (shouldn't be committed)
.git/index.lock

docs/delivery
docs/pbi
docs/operations
.cache/
paper

# MkDocs build output
# Default mkdocs output
site/
# Our configured output dir
public/*
!public/.gitkeep
conan.lock
AGENTS.md
.yams*
CMakeUserPresets.json
