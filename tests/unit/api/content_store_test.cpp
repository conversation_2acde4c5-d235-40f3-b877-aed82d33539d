#include <yams/api/async_content_store.h>
#include <yams/api/content_metadata.h>
#include <yams/api/content_store.h>
#include <yams/api/content_store_builder.h>
#include <yams/api/content_store_error.h>
#include <yams/api/progress_reporter.h>

#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include <filesystem>
#include <fstream>
#include <random>
#include <thread>

namespace yams::api::test {

namespace fs = std::filesystem;
using namespace std::chrono_literals;

// Test fixture
class ContentStoreTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create temporary test directory
        testDir_ =
            fs::temp_directory_path() / ("kronos_test_" + std::to_string(std::random_device{}()));
        fs::create_directories(testDir_);

        // Create content store
        auto result = ContentStoreBuilder::createDefault(testDir_);
        ASSERT_TRUE(result.has_value()) << "Failed to create content store";
        store_ = std::move(result).value();
    }

    void TearDown() override {
        store_.reset();
        // Clean up test directory
        fs::remove_all(testDir_);
    }

    // Helper to create test file
    fs::path createTestFile(const std::string& name, const std::string& content) {
        auto path = testDir_ / name;
        std::ofstream file(path);
        file << content;
        file.close();
        return path;
    }

    // Helper to read file content
    std::string readFile(const fs::path& path) {
        std::ifstream file(path);
        return std::string(std::istreambuf_iterator<char>(file), std::istreambuf_iterator<char>());
    }

    fs::path testDir_;
    std::unique_ptr<IContentStore> store_;
};

// Basic store and retrieve test
TEST_F(ContentStoreTest, StoreAndRetrieveFile) {
    // Create test file
    std::string content = "Hello, Kronos!";
    auto inputPath = createTestFile("test.txt", content);

    // Store file
    ContentMetadata metadata;
    metadata.mimeType = "text/plain";
    metadata.name = "test.txt";

    auto storeResult = store_->store(inputPath, metadata);
    ASSERT_TRUE(storeResult.has_value()) << "Store failed";
    EXPECT_FALSE(storeResult.value().contentHash.empty());
    EXPECT_EQ(storeResult.value().bytesStored, content.size());

    // Retrieve file
    auto outputPath = testDir_ / "retrieved.txt";
    auto retrieveResult = store_->retrieve(storeResult.value().contentHash, outputPath);
    ASSERT_TRUE(retrieveResult.has_value()) << "Retrieve failed";
    EXPECT_TRUE(retrieveResult.value().found);
    EXPECT_EQ(retrieveResult.value().size, content.size());

    // Verify content
    EXPECT_EQ(readFile(outputPath), content);
}

// Test deduplication
TEST_F(ContentStoreTest, Deduplication) {
    std::string content = "Duplicate content for testing";

    // Store first file
    auto file1 = createTestFile("dup1.txt", content);
    auto result1 = store_->store(file1);
    ASSERT_TRUE(result1.has_value());

    // Store second file with same content
    auto file2 = createTestFile("dup2.txt", content);
    auto result2 = store_->store(file2);
    ASSERT_TRUE(result2.has_value());

    // Hashes should be the same
    EXPECT_EQ(result1.value().contentHash, result2.value().contentHash);

    // Second store should have deduplicated bytes
    EXPECT_GT(result2.value().bytesDeduped, 0);
    EXPECT_EQ(result2.value().dedupRatio(), 1.0); // 100% deduplication
}

// Test metadata operations
TEST_F(ContentStoreTest, MetadataOperations) {
    // Store file with metadata
    auto file = createTestFile("meta.txt", "Content with metadata");

    ContentMetadata metadata;
    metadata.mimeType = "text/plain";
    metadata.name = "meta.txt";
    metadata.tags["category"] = "test";
    metadata.tags["type"] = "sample";
    metadata.tags["author"] = "test_suite";

    auto storeResult = store_->store(file, metadata);
    ASSERT_TRUE(storeResult.has_value());

    // Get metadata
    auto hash = storeResult.value().contentHash;
    auto metaResult = store_->getMetadata(hash);
    ASSERT_TRUE(metaResult.has_value());

    auto& retrieved = metaResult.value();
    EXPECT_EQ(retrieved.mimeType, metadata.mimeType);
    EXPECT_EQ(retrieved.name, metadata.name);
    EXPECT_EQ(retrieved.tags, metadata.tags);
    EXPECT_EQ(retrieved.tags.at("author"), "test_suite");

    // Update metadata
    ContentMetadata updated = retrieved;
    updated.tags["status"] = "updated";

    auto updateResult = store_->updateMetadata(hash, updated);
    ASSERT_TRUE(updateResult.has_value());

    // Verify update
    auto updatedMeta = store_->getMetadata(hash);
    ASSERT_TRUE(updatedMeta.has_value());
    EXPECT_EQ(updatedMeta.value().tags.at("status"), "updated");
}

// Test stream operations
TEST_F(ContentStoreTest, StreamOperations) {
    std::string content = "Stream test content";

    // Store via stream
    std::istringstream input(content);
    ContentMetadata metadata;
    metadata.name = "stream.txt";

    auto storeResult = store_->storeStream(input, metadata);
    ASSERT_TRUE(storeResult.has_value());

    // Retrieve via stream
    std::ostringstream output;
    auto retrieveResult = store_->retrieveStream(storeResult.value().contentHash, output);
    ASSERT_TRUE(retrieveResult.has_value());

    EXPECT_EQ(output.str(), content);
}

// Test memory operations
TEST_F(ContentStoreTest, MemoryOperations) {
    std::string content = "Memory test";
    std::vector<std::byte> data;
    data.reserve(content.size());
    for (char c : content) {
        data.push_back(static_cast<std::byte>(c));
    }

    // Store bytes
    ContentMetadata metadata;
    metadata.name = "memory.bin";

    auto storeResult = store_->storeBytes(data, metadata);
    ASSERT_TRUE(storeResult.has_value());

    // Retrieve bytes
    auto retrieveResult = store_->retrieveBytes(storeResult.value().contentHash);
    ASSERT_TRUE(retrieveResult.has_value());

    std::string retrieved(reinterpret_cast<const char*>(retrieveResult.value().data()),
                          retrieveResult.value().size());
    EXPECT_EQ(retrieved, content);
}

// Test batch operations
TEST_F(ContentStoreTest, BatchOperations) {
    // Create multiple files
    std::vector<fs::path> files;
    std::vector<ContentMetadata> metadata;

    for (int i = 0; i < 5; ++i) {
        std::string name = "batch" + std::to_string(i) + ".txt";
        std::string content = "Batch content " + std::to_string(i);
        files.push_back(createTestFile(name, content));

        ContentMetadata meta;
        meta.name = name;
        metadata.push_back(meta);
    }

    // Batch store
    auto results = store_->storeBatch(files, metadata);
    ASSERT_EQ(results.size(), files.size());

    std::vector<std::string> hashes;
    for (const auto& result : results) {
        ASSERT_TRUE(result.has_value());
        hashes.push_back(result.value().contentHash);
    }

    // Batch remove
    auto removeResults = store_->removeBatch(hashes);
    ASSERT_EQ(removeResults.size(), hashes.size());

    for (const auto& result : removeResults) {
        ASSERT_TRUE(result.has_value());
        EXPECT_TRUE(result.value());
    }
}

// Test progress reporting
TEST_F(ContentStoreTest, ProgressReporting) {
    // Create larger file for progress testing
    std::string content(1024 * 1024, 'X'); // 1MB
    auto file = createTestFile("large.bin", content);

    std::atomic<int> progressCalls{0};
    std::atomic<uint64_t> lastBytes{0};

    ProgressCallback progressCallback = [&](const Progress& progress) {
        progressCalls++;
        lastBytes = progress.bytesProcessed;
        EXPECT_LE(progress.bytesProcessed, progress.totalBytes);
    };

    ContentMetadata metadata;
    metadata.name = "large.bin";
    auto result = store_->store(file, metadata, progressCallback);
    ASSERT_TRUE(result.has_value());

    EXPECT_GT(progressCalls.load(), 0);
    EXPECT_EQ(lastBytes.load(), content.size());
}

// Test error handling
TEST_F(ContentStoreTest, ErrorHandling) {
    // Test non-existent file
    auto result1 = store_->store(testDir_ / "nonexistent.txt");
    EXPECT_FALSE(result1.has_value());
    EXPECT_EQ(result1.error(), ErrorCode::FileNotFound);

    // Test invalid hash
    auto result2 = store_->retrieve("invalid_hash", testDir_ / "out.txt");
    EXPECT_FALSE(result2.has_value());

    // Test removing non-existent content
    auto result3 = store_->remove("nonexistent_hash");
    EXPECT_TRUE(result3.has_value());
    EXPECT_FALSE(result3.value()); // Should return false, not error
}

// Test exists operation
TEST_F(ContentStoreTest, ExistsOperation) {
    auto file = createTestFile("exists.txt", "Test content");

    auto storeResult = store_->store(file);
    ASSERT_TRUE(storeResult.has_value());

    auto hash = storeResult.value().contentHash;

    // Should exist
    auto exists1 = store_->exists(hash);
    ASSERT_TRUE(exists1.has_value());
    EXPECT_TRUE(exists1.value());

    // Should not exist
    auto exists2 = store_->exists("nonexistent_hash");
    ASSERT_TRUE(exists2.has_value());
    EXPECT_FALSE(exists2.value());
}

// Test health check
TEST_F(ContentStoreTest, HealthCheck) {
    auto health = store_->checkHealth();

    EXPECT_TRUE(health.isHealthy);
    EXPECT_FALSE(health.status.empty());
    EXPECT_TRUE(health.errors.empty());
}

// Test statistics
TEST_F(ContentStoreTest, Statistics) {
    auto initialStats = store_->getStats();

    // Store some files
    for (int i = 0; i < 3; ++i) {
        auto file =
            createTestFile("stats" + std::to_string(i) + ".txt", "Content " + std::to_string(i));
        store_->store(file);
    }

    auto finalStats = store_->getStats();

    EXPECT_GT(finalStats.totalObjects, initialStats.totalObjects);
    EXPECT_GT(finalStats.totalBytes, initialStats.totalBytes);
    EXPECT_GT(finalStats.storeOperations, initialStats.storeOperations);
}

// Async content store tests
class AsyncContentStoreTest : public ContentStoreTest {
protected:
    void SetUp() override {
        ContentStoreTest::SetUp();
        // AsyncContentStore needs a shared_ptr, not unique_ptr
        // For testing, we'll skip async tests for now
        // asyncStore_ = std::make_unique<AsyncContentStore>(store_);
    }

    std::unique_ptr<AsyncContentStore> asyncStore_;
};

// Test async store and retrieve
// Commented out - AsyncContentStore needs refactoring to work with unique_ptr
/*
TEST_F(AsyncContentStoreTest, AsyncStoreAndRetrieve) {
    auto file = createTestFile("async.txt", "Async content");

    // Async store
    auto storeFuture = asyncStore_->storeAsync(file);
    auto storeResult = storeFuture.get();
    ASSERT_TRUE(storeResult.has_value());

    // Async retrieve
    auto outputPath = testDir_ / "async_out.txt";
    auto retrieveFuture = asyncStore_->retrieveAsync(
        storeResult.value().contentHash, outputPath);
    auto retrieveResult = retrieveFuture.get();
    ASSERT_TRUE(retrieveResult.has_value());

    EXPECT_EQ(readFile(outputPath), "Async content");
}

// Test concurrent operations
TEST_F(AsyncContentStoreTest, ConcurrentOperations) {
    const int numFiles = 10;
    std::vector<std::future<Result<StoreResult>>> futures;

    // Launch concurrent stores
    for (int i = 0; i < numFiles; ++i) {
        auto file = createTestFile("concurrent" + std::to_string(i) + ".txt",
                                  "Content " + std::to_string(i));
        futures.push_back(asyncStore_->storeAsync(file));
    }

    // Wait for all to complete
    std::vector<std::string> hashes;
    for (auto& future : futures) {
        auto result = future.get();
        ASSERT_TRUE(result.has_value());
        hashes.push_back(result.value().contentHash);
    }

    // Verify all stored
    for (const auto& hash : hashes) {
        auto exists = asyncStore_->existsAsync(hash).get();
        ASSERT_TRUE(exists.has_value());
        EXPECT_TRUE(exists.value());
    }
}

// Test callback-based async
TEST_F(AsyncContentStoreTest, CallbackAsync) {
    auto file = createTestFile("callback.txt", "Callback test");

    std::promise<Result<StoreResult>> promise;
    auto future = promise.get_future();

    asyncStore_->storeAsync(file,
        [&promise](const Result<StoreResult>& result) {
            promise.set_value(result);
        });

    auto result = future.get();
    ASSERT_TRUE(result.has_value());
    EXPECT_FALSE(result.value().contentHash.empty());
}

// Test max concurrent operations
TEST_F(AsyncContentStoreTest, MaxConcurrentOps) {
    asyncStore_->setMaxConcurrentOperations(2);

    // Launch more operations than allowed
    std::vector<std::future<Result<bool>>> futures;
    for (int i = 0; i < 5; ++i) {
        futures.push_back(asyncStore_->existsAsync("hash" + std::to_string(i)));
    }

    // Check pending operations
    EXPECT_LE(asyncStore_->getPendingOperations(), 2);

    // Wait for all
    for (auto& future : futures) {
        future.get();
    }

    asyncStore_->waitAll();
    EXPECT_EQ(asyncStore_->getPendingOperations(), 0);
}
*/

// Progress reporter tests
TEST(ProgressReporterTest, BasicProgress) {
    ProgressReporter reporter(1000);

    std::vector<Progress> updates;
    reporter.setCallback([&updates](const Progress& p) { updates.push_back(p); });

    reporter.reportProgress(250);
    reporter.reportProgress(500);
    reporter.reportProgress(750);
    reporter.reportProgress(1000);

    ASSERT_GE(updates.size(), 4);
    EXPECT_EQ(updates.back().bytesProcessed, 1000);
    EXPECT_EQ(updates.back().totalBytes, 1000);
    EXPECT_DOUBLE_EQ(updates.back().percentage, 100.0);
}

TEST(ProgressReporterTest, Cancellation) {
    ProgressReporter reporter;

    EXPECT_FALSE(reporter.isCancelled());
    reporter.cancel();
    EXPECT_TRUE(reporter.isCancelled());

    EXPECT_THROW(reporter.throwIfCancelled(), OperationCancelledException);
}

TEST(ProgressReporterTest, SubReporter) {
    ProgressReporter parent(1000);
    auto sub = parent.createSubReporter(500);

    sub->reportProgress(250);
    EXPECT_EQ(parent.getBytesProcessed(), 125); // 250/500 * 500 = 125
}

// Content metadata tests
TEST(ContentMetadataTest, Serialization) {
    ContentMetadata original;
    original.mimeType = "application/json";
    original.name = "data.json";
    original.tags["category"] = "test";
    original.tags["format"] = "json";
    original.tags["version"] = "1.0";

    // Convert to JSON and back
    auto json = original.toJson();
    EXPECT_FALSE(json.empty());

    // Parse from JSON
    auto result = ContentMetadata::fromJson(json);
    ASSERT_TRUE(result.has_value());

    auto& deserialized = result.value();
    EXPECT_EQ(deserialized.mimeType, original.mimeType);
    EXPECT_EQ(deserialized.name, original.name);
    EXPECT_EQ(deserialized.tags, original.tags);
    EXPECT_EQ(deserialized.tags.at("version"), "1.0");
}

TEST(ContentMetadataTest, TagOperations) {
    ContentMetadata metadata;

    EXPECT_FALSE(metadata.tags.count("test"));

    metadata.tags["test"] = "true";
    EXPECT_TRUE(metadata.tags.count("test"));

    metadata.tags["test"] = "true"; // Duplicate
    EXPECT_EQ(metadata.tags.size(), 1);

    metadata.tags.erase("test");
    EXPECT_FALSE(metadata.tags.count("test"));
}

TEST(ContentMetadataTest, MetadataQuery) {
    ContentMetadata metadata;
    metadata.mimeType = "text/plain";
    metadata.name = "test.txt";
    metadata.tags["priority"] = "important";
    metadata.tags["type"] = "document";
    metadata.tags["author"] = "john";

    // Query that matches
    MetadataQuery query1;
    query1.mimeType = "text/plain";
    query1.requiredTags = {"priority"}; // Check for tag key, not value
    EXPECT_TRUE(query1.matches(metadata));

    // Query that doesn't match
    MetadataQuery query2;
    query2.mimeType = "image/png";
    EXPECT_FALSE(query2.matches(metadata));

    // Tag query
    MetadataQuery query3;
    query3.anyTags = {"priority", "type"}; // Check for tag keys
    EXPECT_TRUE(query3.matches(metadata));

    query3.excludeTags = {"draft"};
    EXPECT_TRUE(query3.matches(metadata));

    query3.excludeTags = {"priority"}; // Exclude a tag key that exists
    EXPECT_FALSE(query3.matches(metadata));
}

// Builder tests
TEST(ContentStoreBuilderTest, DefaultBuild) {
    auto tempDir = fs::temp_directory_path() / "builder_test";
    fs::create_directories(tempDir);

    auto result = ContentStoreBuilder::createDefault(tempDir);
    ASSERT_TRUE(result.has_value());

    auto store = std::move(result).value();
    EXPECT_NE(store, nullptr);

    fs::remove_all(tempDir);
}

TEST(ContentStoreBuilderTest, CustomConfig) {
    ContentStoreConfig config;
    config.storagePath = fs::temp_directory_path() / "custom_test";
    config.chunkSize = 128 * 1024;
    config.enableCompression = true;
    config.compressionType = "zstd";
    config.compressionLevel = 5;

    ContentStoreBuilder builder;
    auto result = builder.withConfig(config).build();

    ASSERT_TRUE(result.has_value());

    fs::remove_all(config.storagePath);
}

TEST(ContentStoreBuilderTest, ValidationError) {
    ContentStoreConfig config;
    // Invalid: empty storage path

    auto result = ContentStoreBuilder::createFromConfig(config);
    EXPECT_FALSE(result.has_value());
    EXPECT_EQ(result.error(), ErrorCode::InvalidArgument);
}

} // namespace yams::api::test