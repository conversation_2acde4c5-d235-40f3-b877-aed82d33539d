{"version": 4, "cmakeMinimumRequired": {"major": 3, "minor": 20, "patch": 0}, "configurePresets": [{"name": "base", "hidden": true, "cacheVariables": {"CMAKE_EXPORT_COMPILE_COMMANDS": "ON"}}, {"name": "yams-debug", "displayName": "<PERSON><PERSON><PERSON> (Conan + Ninja)", "description": "Conan toolchain + Ninja, single-config Debug", "inherits": "base", "generator": "Ninja", "binaryDir": "${sourceDir}/build/${presetName}", "toolchainFile": "${sourceDir}/build/${presetName}/build/Debug/generators/conan_toolchain.cmake", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "YAMS_USE_CONAN": "ON"}}, {"name": "yams-release", "displayName": "YAMS Release (Conan + Ninja)", "description": "Conan toolchain + Ninja, single-config Release", "inherits": "base", "generator": "Ninja", "binaryDir": "${sourceDir}/build/${presetName}", "toolchainFile": "${sourceDir}/build/${presetName}/build/Release/generators/conan_toolchain.cmake", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release", "YAMS_USE_CONAN": "ON"}}], "buildPresets": [{"name": "yams-debug", "configurePreset": "yams-debug"}, {"name": "yams-release", "configurePreset": "yams-release"}], "testPresets": [{"name": "yams-debug", "configurePreset": "yams-debug", "output": {"outputOnFailure": true}}, {"name": "yams-release", "configurePreset": "yams-release", "output": {"outputOnFailure": true}}]}