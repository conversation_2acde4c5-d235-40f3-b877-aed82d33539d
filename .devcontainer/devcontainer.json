{
  "name": "YAMS CI DevContainer",
  "build": {
    "dockerfile": "Dockerfile"
  },
  "features": {
    "ghcr.io/devcontainers/features/docker-in-docker:2": {}
  },
  "runArgs": [
    "--init"
  ],
  "containerEnv": {
    "DEBIAN_FRONTEND": "noninteractive"
  },
  "customizations": {
    "vscode": {
      "extensions": [
        "ms-vscode.cpptools",
        "ms-vscode.cmake-tools",
        "twxs.cmake",
            "ms-azuretools.vscode-docker",
            "xaver.clang-format",
            "notskm.clang-tidy"
      ]
    }
  },
  "remoteUser": "vscode",
  "postCreateCommand": "bash /usr/local/share/yams/postCreate.sh",
  "mounts": [
  // Use a named volume for Docker-in-Docker storage; source must be a volume name, not a path
  "source=docker-data,target=/var/lib/docker,type=volume"
  ]
}
