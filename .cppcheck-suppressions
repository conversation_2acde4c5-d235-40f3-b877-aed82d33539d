# YAMS cppcheck suppressions
# This file contains suppressions for false positives and intentional code patterns

# =============================================================================
# Third-party and generated code suppressions
# =============================================================================

# Suppress all issues in third-party dependencies fetched via CMake
*:*/_deps/*
*:*/build/*
*:*/cmake-build-*/*
*:*/.cache/*
*:*/third_party/*
*:*/external/*
*:*/vendor/*

# Generated protobuf files
*:*.pb.h
*:*.pb.cc

# SQLite amalgamation file - external code
*:*/sqlite-vec.c
*:*/sqlite3.c
*:*/sqlite3.h

# PDFium headers - external binary distribution
*:*/pdfium/*

# LZMA SDK - external C code
*:*/lzma-src/*

# ONNX Runtime headers
*:*/onnxruntime/*

# =============================================================================
# Specific unused function suppressions
# =============================================================================

# Test-related functions
unusedFunction:*Mock*
unusedFunction:*Test*
unusedFunction:*Fixture*

# Factory and registry patterns
unusedFunction:*Factory*
unusedFunction:*Provider*
unusedFunction:*Handler*
unusedFunction:*Processor*

# CLI command registration
unusedFunction:*Command*

# Configuration and initialization
unusedFunction:*Init*
unusedFunction:*Config*
unusedFunction:*Settings*

# RAII and resource management
unusedFunction:*Guard*
unusedFunction:*Lock*
unusedFunction:*Scope*

# API interfaces
unusedFunction:*Interface*
unusedFunction:*Abstract*

# Error handling patterns
unusedFunction:*Result*
unusedFunction:*Expected*
unusedFunction:*Optional*
unusedFunction:*Cleanup*

# =============================================================================
# Other common suppressions
# =============================================================================

# Intentionally unused variables
unusedVariable:*unused*
unusedVariable:*_unused*

# Template and inheritance patterns
missingOverride
noExplicitConstructor
templateInstantiation
useInitializationList

# Duplicate expressions in specialized code
duplicateExpression:*vector*
duplicateExpression:*embedding*
duplicateExpression:*compression*
duplicateExpression:*crypto*
duplicateExpression:*hash*
duplicateExpression:*sql*
duplicateExpression:*query*

# Performance-critical code where style is less important
constParameter:*hot_path*
constParameter:*performance*
constParameter:*fast_*