YAMS (Yet Another Memory System)
NOTICE

Copyright (c) 2025 trvon
Copyright (c) 2025 YAMS contributors

This product includes software developed by the YAMS Team.
Project home: https://github.com/trvon/yams

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at:

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software distributed
under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
CONDITIONS OF ANY KIND, either express or implied. See the License for the
specific language governing permissions and limitations under the License.

Trademarks: YAMS and any related names, logos, or marks are trademarks or
registered trademarks of their respective owners. Use of these marks does not
imply endorsement.

Third-Party Notices
The YAMS project uses and/or integrates third‑party components. Those components
remain under their respective licenses. License texts are provided by their
upstream projects and/or included alongside the sources when bundled.

Examples of third‑party components used in this repository or during builds:
- GoogleTest (BSD-3-Clause) — used for unit testing. See: _deps/googletest-src/LICENSE
- SQLite (Public Domain) — used for database/FTS functionality. See upstream: https://www.sqlite.org
- Other dependencies may be fetched via build tooling or package managers; consult the corresponding
  upstream repositories and LICENSE/NOTICE files for complete and current terms.

SPDX
Where practical, source files may include an SPDX identifier header:
    SPDX-License-Identifier: Apache-2.0
This helps automated tooling recognize the project’s license.

End of NOTICE
