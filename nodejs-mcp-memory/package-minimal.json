{"name": "yams-nodejs-mcp-memory", "version": "1.0.0", "description": "Node.js MCP Memory Server - Persistent content storage for LLMs", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "node --watch index.js", "init": "node scripts/init-storage.js", "test": "node --test tests/**/*.test.js", "install-deps": "npm install --no-optional", "check": "node scripts/check-installation.js"}, "keywords": ["mcp", "memory", "storage", "llm", "content-addressed", "deduplication", "search"], "author": "YAMS Contributors", "license": "Apache-2.0", "engines": {"node": ">=18.0.0"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "better-sqlite3": "^9.2.2", "mime-types": "^2.1.35", "uuid": "^9.0.1", "chalk": "^5.3.0", "winston": "^3.11.0", "dotenv": "^16.3.1"}, "optionalDependencies": {"sqlite-vec": "^0.1.0", "xxhash-wasm": "^1.0.2", "lz4": "^0.6.5", "commander": "^11.1.0", "fast-glob": "^3.3.2", "chokidar": "^3.5.3"}, "devDependencies": {"eslint": "^8.56.0", "prettier": "^3.1.1", "@types/node": "^20.10.6"}}